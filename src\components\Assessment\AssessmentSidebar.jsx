import { Clock, ChevronRight, Shuffle } from 'lucide-react';

const AssessmentSidebar = ({
  assessmentData,
  answers,
  currentPage,
  setCurrentPage,
  questionsPerPage,
  currentStep,
  totalSteps,
  onFillRandomAnswers
}) => {
  // Define assessment phases mapping (reordered to match the design)
  const assessmentPhases = [
    {
      id: 1,
      title: "Phase 1",
      subtitle: "Big Five Personality",
      assessmentKey: "bigFive",
      step: 3, // Big Five is step 3 in the flow
      totalQuestions: 44
    },
    {
      id: 2,
      title: "Phase 2",
      subtitle: "RIASEC Holland Codes",
      assessmentKey: "riasec",
      step: 2, // RIASEC is step 2 in the flow
      totalQuestions: 60
    },
    {
      id: 3,
      title: "Phase 3",
      subtitle: "VIA Character Strengths",
      assessmentKey: "via",
      step: 1, // VIA is step 1 in the flow
      totalQuestions: 96 // Still 96 questions, just grouped into 6 categories instead of 24
    }
  ];

  // Get current phase based on current step
  const getCurrentPhase = () => {
    return assessmentPhases.find(phase => phase.step === currentStep);
  };

  // Calculate category progress
  const getCategoryProgress = (categoryKey) => {
    const category = assessmentData.categories[categoryKey];
    if (!category) return { answered: 0, total: 0 };
    
    let total = category.questions.length;
    if (category.reverseQuestions) {
      total += category.reverseQuestions.length;
    }
    
    let answered = 0;
    // Count regular questions
    category.questions.forEach((_, index) => {
      const questionKey = `${categoryKey}_${index}`;
      if (answers[questionKey] !== undefined) answered++;
    });
    
    // Count reverse questions
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((_, index) => {
        const questionKey = `${categoryKey}_reverse_${index}`;
        if (answers[questionKey] !== undefined) answered++;
      });
    }
    
    return { answered, total };
  };

  // Calculate total progress for current assessment
  const getTotalProgress = () => {
    const allQuestions = [];
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      // Regular questions
      category.questions.forEach((_, index) => {
        allQuestions.push(`${categoryKey}_${index}`);
      });
      
      // Reverse questions
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          allQuestions.push(`${categoryKey}_reverse_${index}`);
        });
      }
    });

    const answered = allQuestions.filter(questionKey => answers[questionKey] !== undefined).length;
    return { answered, total: allQuestions.length };
  };

  // Navigate to category
  const navigateToCategory = (categoryKey) => {
    const allQuestions = [];
    let categoryStartIndex = 0;
    let found = false;

    Object.entries(assessmentData.categories).forEach(([catKey, category]) => {
      if (catKey === categoryKey) {
        found = true;
        return;
      }
      
      if (!found) {
        // Count questions before this category
        categoryStartIndex += category.questions.length;
        if (category.reverseQuestions) {
          categoryStartIndex += category.reverseQuestions.length;
        }
      }
    });

    const targetPage = Math.floor(categoryStartIndex / questionsPerPage);
    setCurrentPage(targetPage);
  };

  const currentPhase = getCurrentPhase();
  const totalProgress = getTotalProgress();

  return (
    <div className="hidden lg:block fixed right-0 top-0 h-full w-80 bg-white shadow-xl border-l border-gray-100 overflow-y-auto z-20">
      <div className="p-6 h-full flex flex-col">
        {/* Assessment Info */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center space-x-2">
            <div className="p-1 bg-indigo-100 rounded">
              <Clock className="h-4 w-4 text-indigo-600" />
            </div>
            <span>Assessment Progress</span>
          </h3>

          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-indigo-600">
                {totalProgress.answered}
              </div>
              <div className="text-xs text-gray-500">Completed</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-400">
                {totalProgress.total - totalProgress.answered}
              </div>
              <div className="text-xs text-gray-500">Remaining</div>
            </div>
          </div>

          {/* Random Fill Button - Only show in development */}
          {import.meta.env.DEV && onFillRandomAnswers && (
            <button
              onClick={onFillRandomAnswers}
              className="w-full mb-4 flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
            >
              <Shuffle className="h-4 w-4" />
              <span className="text-sm font-medium">Fill Random Answers</span>
            </button>
          )}
        </div>

        {/* Phase Structure */}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Assessment Structure
          </h3>

          <div className="space-y-4">
            {assessmentPhases.map((phase) => {
              const isCurrentPhase = phase.step === currentStep;
              // Show actual progress for current phase, total questions for others
              const phaseProgress = isCurrentPhase ? totalProgress : { answered: 0, total: phase.totalQuestions };

              return (
                <div key={phase.id} className={`border rounded-lg p-4 ${isCurrentPhase ? 'border-indigo-200 bg-indigo-50' : 'border-gray-200 bg-gray-50'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className={`font-semibold ${isCurrentPhase ? 'text-indigo-900' : 'text-gray-700'}`}>
                        {phase.title}
                      </h4>
                      <p className={`text-sm ${isCurrentPhase ? 'text-indigo-700' : 'text-gray-500'}`}>
                        {phase.subtitle}
                      </p>
                    </div>
                    <div className={`text-sm font-medium ${isCurrentPhase ? 'text-indigo-600' : 'text-gray-400'}`}>
                      {phaseProgress.answered}/{phaseProgress.total}
                    </div>
                  </div>

                  {/* Categories for current phase */}
                  {isCurrentPhase && (
                    <div className="mt-3 space-y-2">
                      {Object.entries(assessmentData.categories).map(([categoryKey, category]) => {
                        const categoryProgress = getCategoryProgress(categoryKey);
                        const isCompleted = categoryProgress.answered === categoryProgress.total;
                        
                        return (
                          <button
                            key={categoryKey}
                            onClick={() => navigateToCategory(categoryKey)}
                            className={`w-full text-left p-3 rounded-lg transition-all duration-200 hover:scale-105 ${
                              isCompleted 
                                ? 'bg-green-100 border border-green-200 text-green-800' 
                                : categoryProgress.answered > 0
                                ? 'bg-blue-100 border border-blue-200 text-blue-800'
                                : 'bg-white border border-gray-200 text-gray-600 hover:bg-gray-50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">
                                {category.name}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className="text-xs">
                                  {categoryProgress.answered}/{categoryProgress.total}
                                </span>
                                <ChevronRight className="h-3 w-3" />
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Total Progress - Bottom Section */}
        <div className="mt-auto pt-6 border-t border-gray-200">
          <div className="text-center mb-4">
            <h4 className="text-base font-medium text-gray-600 mb-3">Current Phase Progress</h4>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-indigo-600 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${totalProgress.total > 0 ? (totalProgress.answered / totalProgress.total) * 100 : 0}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-500 mt-2">
              {totalProgress.total > 0 ? Math.round((totalProgress.answered / totalProgress.total) * 100) : 0}% Complete
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentSidebar;
